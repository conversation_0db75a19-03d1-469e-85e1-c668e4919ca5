{"compilerOptions": {"target": "ES2022", "lib": ["ES2023", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": false, "noUnusedParameters": false, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./client/src/*"], "@shared/*": ["./shared/*"], "@assets/*": ["./client/attached_assets/*"]}, "types": ["node"]}, "include": ["client/src/**/*", "server/**/*", "shared/**/*"], "exclude": ["node_modules", "dist", "migrations"]}