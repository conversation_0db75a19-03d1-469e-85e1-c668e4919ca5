# VMake Product Catalog - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL connection string
# Format: postgresql://username:password@host:port/database_name
# Example: postgresql://myuser:mypassword@localhost:5432/vmake_catalog
DATABASE_URL=postgresql://username:password@localhost:5432/your_database_name

# =============================================================================
# SESSION CONFIGURATION
# =============================================================================
# Secret key for session encryption (use a strong, random string)
# Generate with: node -e "console.log(require('crypto').randomBytes(32).toString('hex'))"
SESSION_SECRET=your-super-secret-session-key-here

# =============================================================================
# ADMIN CONFIGURATION
# =============================================================================
# Admin WhatsApp number (include country code)
# Example: +1234567890
ADMIN_WHATSAPP=+1234567890

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Bcrypt salt rounds for password hashing (10-12 recommended)
BCRYPT_SALT_ROUNDS=12

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Rate limit window in milliseconds (15 minutes = 900000)
RATE_LIMIT_WINDOW_MS=900000

# Maximum requests per window for general API
RATE_LIMIT_MAX_REQUESTS=100

# Maximum authentication attempts per window
AUTH_RATE_LIMIT_MAX_REQUESTS=5

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
# Maximum file size in bytes (100MB = 104857600)
MAX_FILE_SIZE=104857600

# =============================================================================
# DEVELOPMENT CONFIGURATION (Optional)
# =============================================================================
# Set to 'development' for local development, 'production' for deployment
NODE_ENV=development

# Port for local development (default: 5500)
PORT=5500
